<script setup>
import { gameService } from "@/services/game.service";
import { useTemplateStore } from "@/stores/template";
import { onMounted, ref, computed, watch } from "vue";
import useNotify from "@/composables/useNotify";

const store = useTemplateStore();
const { setNotify } = useNotify();

const props = defineProps(["v$", "state", "idModal"]);

const { v$, state } = props;

const apiUrl = import.meta.env.VITE_API_URL.replace(/\/$/, "");

const fullAvatarUrl = computed(() => {
  if (!state.avatar) return "";
  // console.log("Full avatar URL:", `${apiUrl}${state.avatar}`);
  return `${apiUrl}${state.avatar}`;
});

const fileInputRef = ref([]);

const triggerFileSelect1 = (index) => {
  fileInputRef.value[index]?.click();
};

const handleFileChange = async (event, index) => {
  const file = event.target.files[0];
  if (!file) return;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("type", "avatar");
  let payload;
  payload = formData;
  try {
    store.pageLoader({ mode: "on" });
    const response = await gameService.upload(payload);
    state[index].avatar = response?.path;
    state[index].avatar = `${apiUrl}${state[index].avatar}`;
    store.pageLoader({ mode: "off" });
  } catch (e) {
    console.log(e);
    store.pageLoader({ mode: "off" });
    setNotify({
      title: "Error",
      message: "Không đúng định dạng ảnh",
    });
  }
};

const addNew = () => {
  state.push({
    title: null,
    avatar: null,
  });
};

watch(
  () => props.state,
  () => {
    console.log(props.state);
  },
  { deep: true }
);

onMounted(async () => {
  try {
    store.pageLoader({ mode: "on" });
    store.pageLoader({ mode: "off" });
  } catch (error) {
    store.pageLoader({ mode: "off" });
  }
});
</script>

<template>
  <div class="content">
    <div class="row justify-content-start">
      <div class="col-sm-12 col-md-12">
        <div
          class="row justify-content-center"
          v-for="(item, index) in props.state"
          :key="index"
          style="margin-bottom: 20px; border: 1px solid #ccc; padding: 10px"
        >
          <div class="col-sm-10 col-md-12">
            <div class="mb-4">
              <label class="form-label" for="block-form-title-id"
                >Tiêu đề <span class="text-danger">*</span></label
              >
              <input
                type="text"
                class="form-control"
                id="block-form-title-id"
                name="block-form-title-id"
                placeholder="Tiêu đề..."
                v-model="item.title"
              />
            </div>
            <div class="mb-4">
              <label class="form-label" for="block-form-email-id"
                >Ảnh <span class="text-danger">*</span></label
              >
              <input
                :ref="(el) => (fileInputRef[index] = el)"
                type="file"
                class="form-control"
                id="block-form-email-id"
                name="block-form-email-id"
                placeholder="Enter avatar.."
                accept="image/*"
                @change="handleFileChange($event, index)"
                style="display: none"
              />
              <button
                style="margin-left: 10px"
                @click.prevent="triggerFileSelect1(index)"
                class="btn btn-primary"
              >
                Chọn ảnh
              </button>
              <div style="margin-top: 10px" v-if="item.avatar">
                <img :src="`${item.avatar}`" alt="Avatar" width="100" />
              </div>
            </div>
            <button
              type="button"
              class="btn btn-danger align-items-center"
              @click="state.splice(index, 1)"
            >
              Xóa
            </button>
          </div>
        </div>
        <button
          style="margin-bottom: 10px"
          type="button"
          class="btn btn-primary"
          @click="addNew"
        >
          Thêm bài viết
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import "vue-select/dist/vue-select.css";
@import "@/assets/scss/vendor/vue-select";
.icon_arrow_left {
  background-color: rgb(243, 235, 235);
  border-radius: 100%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error {
  width: 100%;
  margin-top: 0.375rem;
  font-size: 0.875rem;
  color: var(--bs-form-invalid-color);
}

.ql-container {
  height: 150px !important;
}
</style>
<style scoped>
.editor-container {
  max-width: 800px;
  margin: 20px auto;
}
.ql-container {
  height: 150px !important;
}
</style>
