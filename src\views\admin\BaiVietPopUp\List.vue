<script setup>
import EButton from "@/components/Elements/EButton.vue";
import EListEmpty from "@/components/Elements/EListEmpty.vue";
import EModal from "@/components/Elements/EModal.vue";
import useNotify from "@/composables/useNotify";
import { baivietPopupService } from "@/services/baiviet-popup.service";
import { useTemplateStore } from "@/stores/template";
import ModalFormCode from "@/views/admin/BaiVietPopUp/Form.vue";
import useVuelidate from "@vuelidate/core";
import { required, sameAs } from "@vuelidate/validators";
import { userService } from "@/services/user.service";
import Swal from "sweetalert2";
import { computed, onMounted, reactive, ref, watch } from "vue";
import ChangePassword from "@/views/admin/MultiTenant/ChangePassword.vue";
import { useForm } from "vee-validate";
import * as yup from "yup";

import {
  Dataset,
  DatasetInfo,
  DatasetItem,
  DatasetPager,
  DatasetSearch,
  DatasetShow,
} from "vue-dataset";
import { useRoute } from "vue-router";

let toast = Swal.mixin({
  buttonsStyling: false,
  target: "#page-container",
  customClass: {
    confirmButton: "btn btn-success m-1",
    cancelButton: "btn btn-danger m-1",
    input: "form-control",
  },
});
const store = useTemplateStore();
const { setNotify } = useNotify();
const route = useRoute();
const id = route.params?.id;
let state = reactive([
  {
    title: "",
    avatar: null,
  },
]);

const refBtn = ref(null);
const refBtnPassword = ref(null);
const domModal = ref("changePassword");
onMounted(() => {
  refBtn.value = document.getElementById("closeModal");
  refBtnPassword.value = document.getElementById("closeModal" + domModal.value); // Gán refBtn cho một phần tử DOM
});

const rules = computed(() => {
  return {};
});

let vformMultiTenant$ = useVuelidate(rules, state);
const validationSchema = yup.object({
  content: yup.string().trim(),
});

const { values, handleSubmit, setFieldValue, resetForm, setFieldError } =
  useForm({
    validationSchema,
    initialValues: {
      content: null,
    },
  });

// Helper variables
const cols = reactive([
  {
    name: "Tên tin tức",
    field: "name",
    sort: "",
  },
  {
    name: "Nội dung",
    field: "status",
    sort: "",
  },
]);

const listTinTuc = ref([]);
const idModal = ref(null);

const onFetchList = async () => {
  try {
    store.pageLoader({ mode: "on" });
    const response = await baivietPopupService.getList();
    console.log(response);
    if (!response?.error) {
      listTinTuc.value = response?.data || [];
      state = JSON.parse(response?.data).map((item) => ({
        title: item.title,
        avatar: item.image_link,
      }));
      console.log(state);
    }
    store.pageLoader({ mode: "off" });
  } catch (error) {
    console.log(error);
    store.pageLoader({ mode: "off" });
  }
};

const handleModalForm = async () => {
  idModal.value = undefined;
  vformMultiTenant$.value.$reset();
  await onFetchList();
  store.pageLoader({ mode: "on" });
  store.pageLoader({ mode: "off" });
};
const handleModalFormUpdate = async (id) => {
  idModal.value = id;
  store.pageLoader({ mode: "on" });
  await apiGetPrinter(id);
  store.pageLoader({ mode: "off" });
};

const apiGetPrinter = async (id) => {
  onFetchList();
  vformMultiTenant$.value.$reset();
};

let stateChangePassword = reactive({
  newPassword: null,
  confirmPassword: null,
});

onMounted(async () => {
  await onFetchList();

  // Remove labels from
  document.querySelectorAll("#datasetLength label").forEach((el) => {
    el.remove();
  });

  // Replace select classes
  let selectLength = document.querySelector("#datasetLength select");

  if (selectLength) {
    selectLength.classList = "";
    selectLength.classList.add("form-select");
    selectLength.style.width = "80px";
  }
});

async function onSubmitMultiTenant() {
  if (idModal.value) {
    await onSubmitUpdateMultiTenant();
  } else {
    await onSubmitCreateMultiTenant();
  }
}
async function confirmNew(val) {
  await onSubmitCreateMultiTenant(val);
}

async function onSubmitCreateMultiTenant(val) {
  try {
    const result = await vformMultiTenant$.value.$validate();
    if (!result) return;
    // if (!values.content) return;
    let payload = {};
    payload = {
      name: "bai-viet-pop-up",
      data: JSON.stringify(state),
    };
    const response = await baivietPopupService.create(payload);
    if (!response?.error) {
      onFetchList();
      if (!val) {
        refBtn.value.click();
      }
      vformMultiTenant$.value.$reset();
      return setNotify({
        title: "Success",
        message: "Create success",
        type: "success",
      });
    }
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

function stripHtml(html) {
  const tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return (
    tmp.textContent.replace(/\n/g, " ") ||
    tmp.innerText.replace(/\n/g, " ") ||
    ""
  );
}

async function onSubmitUpdateMultiTenant() {
  try {
    const result = await vformMultiTenant$.value.$validate();
    if (!result) return;
    // if (!values.content) return;
    let payload = {
      title: state.title,
      content: state.content,
      short_description: state.short_description,
      avatar_url: state.avatar,
      is_active: state.is_active ? 1 : 0,
    };
    const response = await baivietPopupService.update(idModal.value, payload);
    if (!response?.error) {
      onFetchList();
      refBtn.value.click();
      return setNotify({
        title: "Success",
        message: "Update success",
        type: "success",
      });
    }
  } catch (e) {
    return setNotify({
      title: "Error",
      message: e?.message,
    });
  }
}

const onOpenDeleteConfirm = (id) => {
  toast
    .fire({
      title: "Bạn có chắc chắn muốn xóa?",
      text: "Bạn chắc chắn muốn xóa bài viết này!",
      icon: "warning",
      showCancelButton: true,
      customClass: {
        confirmButton: "btn btn-danger m-1",
        cancelButton: "btn btn-info m-1",
      },
      confirmButtonText: "Yes, delete!",
      html: false,
      preConfirm: () => {
        return baivietPopupService.delete(id);
      },
    })
    .then((result) => {
      if (result.dismiss === "cancel") {
        return toast.fire("Cancelled", "Hủy xóa bài viết thành công.", "error");
      }
      toast.fire("Deleted!", "Xóa bài viết thành công.", "success");
      onFetchList();
    });
};

const changeHot = async (id) => {
  const url = `https://68gaming.pro/post?id=${id}`;
  window.open(url, "_blank");
};

const onCloseMultiTenant = () => {
  vformMultiTenant$.value.$reset();
};
</script>

<template>
  <BasePageHeading
    title="Danh sách bài viết pop up"
    subtitle=""
    :go-back="true"
  >
    <template #extra>
      <div class="flex flex-col">
        <div class="d-flex justify-content-end mb-2">
          <EButton
            style="margin-right: 10px"
            type="info"
            size="sm"
            data-bs-toggle="modal"
            data-bs-target="#modal-multi-tenant"
            data-target=".bd-example-modal-lg"
            @click="() => handleModalForm()"
            ><i class="fa fa-plus opacity-50 me-1"></i> Thêm bài viết pop
            up</EButton
          >
        </div>
      </div>
    </template>
  </BasePageHeading>

  <div class="content">
    <BaseBlock title="Danh sách bài viết pop up"> </BaseBlock>
  </div>
  <form @submit.prevent="onSubmitMultiTenant">
    <EModal
      id="modal-multi-tenant"
      :title="idModal ? 'Cập nhật bài viết pop up' : 'Thêm bài viết pop up'"
      :idModal="idModal"
      size="modal-xl"
      ok-text="Confirm"
      :is-show-text-add="false"
      ok-type="submit"
      :close-on-submit="false"
      @confirm="() => onSubmitMultiTenant()"
      @closeModal="() => onCloseMultiTenant()"
    >
      <template v-slot:childrenComponent>
        <ModalFormCode
          :idModal="idModal"
          :v$="vformMultiTenant$"
          :state="state"
        />
      </template>
    </EModal>
  </form>
</template>

<style lang="scss" scoped>
.gg-select {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(1);
  width: 22px;
  height: 22px;
}
.gg-select::after,
.gg-select::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  left: 7px;
  transform: rotate(-45deg);
}
.gg-select::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
  bottom: 4px;
  opacity: 0.3;
}
.gg-select::after {
  border-right: 2px solid;
  border-top: 2px solid;
  top: 4px;
  opacity: 0.3;
}
th.sort {
  cursor: pointer;
  user-select: none;
  &.asc {
    .gg-select::after {
      opacity: 1;
    }
  }
  &.desc {
    .gg-select::before {
      opacity: 1;
    }
  }
}
.ql-container {
  height: 150px !important;
}
</style>
