import { http } from "./Base/base.service";
export const baivietPopupService = {
    async getList(query) {
        return await http.get('/api/v1/public-account/cms/bai-viet-pop-up', {
            params: query
        })
    },
    async create(payload) {
        return await http.post('/api/v1/admins/cms', payload)
    },
    async update(id, payload) {
        return await http.put('/api/v1/admins/posts/' + id, payload)

    },
    async getDetail(id) {
        return await http.get(`/api/v1/admins/posts/${id}`)
    },
    async delete(id) {
        return await http.delete(`/api/v1/admins/posts/${id}`)
    },

    async loginStore(id) {
        return await http.get(`/store-login/${id}`)
    },

    async upload(payload) {
        return await http.post('/api/v1/admins/upload', payload, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },
}
